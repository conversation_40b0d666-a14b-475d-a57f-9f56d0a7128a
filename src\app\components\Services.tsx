'use client';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export default function Services() {
  const servicesRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const servicesElement = servicesRef.current;
    if (!servicesElement) return;

    // Wait a bit for the DOM to be fully loaded
    const timer = setTimeout(() => {
      // Find the "more cases coming soon" section in Work component
      const moreCasesSection = Array.from(document.querySelectorAll('section')).find(section =>
        section.textContent?.includes('more cases coming soon')
      );

      if (!moreCasesSection) {
        console.warn('More cases section not found');
        return;
      }

      // Create ScrollTrigger for the overlay effect
      ScrollTrigger.create({
        trigger: moreCasesSection,
        start: "bottom 60%", // Start earlier - when bottom of "more cases" is at 60% from top of viewport
        end: "bottom -20%", // End later for slower animation
        scrub: 4, // Slower scrub for smoother animation
        pin: false,
        onEnter: () => {
          // Switch to fixed position and start from bottom
          gsap.set(servicesElement, {
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            zIndex: 20,
            overflow: 'hidden', // Prevent scrollbar in fixed mode
            y: '100vh' // Start from bottom
          });

          // Hide body scrollbar during animation
          document.body.style.overflowX = 'hidden';
        },
        onUpdate: (self) => {
          const progress = self.progress;

          // Only animate if we're in fixed mode
          if (servicesElement.style.position === 'fixed') {
            // Move services section from bottom (100vh) to top (0) based on progress
            const yPos = 100 - (progress * 100);
            gsap.set(servicesElement, {
              y: `${yPos}vh`
            });
          }
        },
        onLeave: () => {
          // When animation is complete, make it normal positioned again
          gsap.set(servicesElement, {
            position: 'relative',
            top: 'auto',
            left: 'auto',
            width: 'auto',
            height: 'auto',
            zIndex: 'auto',
            overflow: 'hidden',
            y: 0 // Reset transform
          });

          // Restore body scrollbar
          document.body.style.overflowX = 'hidden'; // Keep hidden to prevent horizontal scroll
        },
        onLeaveBack: () => {
          // When scrolling back, reset to normal position
          gsap.set(servicesElement, {
            position: 'relative',
            top: 'auto',
            left: 'auto',
            width: 'auto',
            height: 'auto',
            zIndex: 'auto',
            overflow: 'hidden',
            y: 0 // Reset transform
          });

          // Restore body scrollbar
          document.body.style.overflowX = 'hidden';
        }
      });

    }, 100);

    return () => {
      clearTimeout(timer);
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section
      ref={servicesRef}
      className="bg-black text-white min-h-screen w-full flex items-center justify-center"
      style={{
        fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
        position: 'relative', // Start as normal positioned
        overflow: 'hidden'
      }}
    >
      <div className="container mx-auto px-4 text-center">
        {/* Top label - SERVICES at very left of section */}
        <div style={{
          position: 'absolute',
          top: 120,
          left: 40,
          color: '#fff',
          fontSize: 28,
          fontWeight: 700,
          letterSpacing: 1.5,
          zIndex: 10,
        }}>
          (SERVICES)
        </div>

        {/* Right label - 04 in orange */}
        <div style={{
          position: 'absolute',
          top: 'clamp(420px, 50vh, 580px)',
          right: 'clamp(20px, 3vw, 40px)',
          color: '#e8561c',
          fontSize: 'clamp(21px, 3.36vw, 42px)',
          fontWeight: 700,
          letterSpacing: 1.5,
          zIndex: 10,
        }}>
          (04)
        </div>

        <h2 className="text-3xl font-bold mb-8">Services</h2>
        <div>
          <p className="text-lg">No services available at the moment.</p>
        </div>
      </div>
    </section>
  );
}


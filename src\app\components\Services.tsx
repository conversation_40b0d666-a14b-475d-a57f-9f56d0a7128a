'use client';
import { useEffect, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';

export default function Services() {
  const [isVisible, setIsVisible] = useState(false);

  // Get global scroll progress
  const { scrollY } = useScroll();

  // Transform scroll to y position - will be controlled by scroll listener
  const y = useTransform(scrollY, (value) => {
    if (!isVisible) return "100vh";

    // Find the more cases section dynamically
    const moreCasesSection = Array.from(document.querySelectorAll('section')).find(section =>
      section.textContent?.includes('more cases coming soon')
    );

    if (!moreCasesSection) return "100vh";

    const rect = moreCasesSection.getBoundingClientRect();
    const windowHeight = window.innerHeight;

    // Calculate progress based on how much of the "more cases" section has scrolled past
    const sectionTop = rect.top + window.scrollY;
    const sectionBottom = rect.bottom + window.scrollY;
    const triggerStart = sectionTop + (rect.height * 0.4); // Start at 40% of section
    const triggerEnd = sectionBottom + windowHeight; // End after section + one viewport

    const progress = Math.max(0, Math.min(1, (value - triggerStart) / (triggerEnd - triggerStart)));

    return `${100 - (progress * 100)}vh`;
  });

  useEffect(() => {
    const handleScroll = () => {
      // Find the "more cases coming soon" section
      const moreCasesSection = Array.from(document.querySelectorAll('section')).find(section =>
        section.textContent?.includes('more cases coming soon')
      );

      if (moreCasesSection) {
        const rect = moreCasesSection.getBoundingClientRect();
        const windowHeight = window.innerHeight;

        // Start animation when 40% of "more cases" section has been scrolled through
        const shouldShow = rect.top <= windowHeight * 0.6;
        setIsVisible(shouldShow);
      }
    };

    // Initial check
    handleScroll();

    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <motion.section
        className="bg-black text-white min-h-screen w-full flex items-center justify-center"
        style={{
          fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          zIndex: 20,
          y: y // Framer Motion transform
        }}
      >
        <div className="container mx-auto px-4 text-center">
          {/* Top label - SERVICES at very left of section */}
          <div style={{
            position: 'absolute',
            top: 120,
            left: 40,
            color: '#fff',
            fontSize: 28,
            fontWeight: 700,
            letterSpacing: 1.5,
            zIndex: 10,
          }}>
            (SERVICES)
          </div>

          {/* Right label - 04 in orange */}
          <div style={{
            position: 'absolute',
            top: 'clamp(420px, 50vh, 580px)',
            right: 'clamp(20px, 3vw, 40px)',
            color: '#e8561c',
            fontSize: 'clamp(21px, 3.36vw, 42px)',
            fontWeight: 700,
            letterSpacing: 1.5,
            zIndex: 10,
          }}>
            (04)
          </div>

          <h2 className="text-3xl font-bold mb-8">Services</h2>
          <div>
            <p className="text-lg">No services available at the moment.</p>
          </div>
        </div>
      </motion.section>
  );
}


'use client';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export default function Services() {
  const servicesRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const servicesElement = servicesRef.current;
    if (!servicesElement) return;

    // Find the "more cases coming soon" section in Work component
    const moreCasesSection = document.querySelector('section:has-text("more cases coming soon")') ||
                            Array.from(document.querySelectorAll('section')).find(section =>
                              section.textContent?.includes('more cases coming soon')
                            );

    if (!moreCasesSection) return;

    // Create ScrollTrigger for the overlay effect
    ScrollTrigger.create({
      trigger: moreCasesSection,
      start: "center center", // When the "more cases" section reaches the center of viewport
      end: "bottom top",
      onEnter: () => {
        // Make services section slide over from the bottom
        gsap.set(servicesElement, {
          position: 'fixed',
          top: '100vh',
          left: 0,
          width: '100%',
          zIndex: 10
        });

        gsap.to(servicesElement, {
          top: 0,
          duration: 0.8,
          ease: "power2.out"
        });
      },
      onLeave: () => {
        // Continue normal scroll behavior
        gsap.set(servicesElement, {
          position: 'relative',
          top: 'auto',
          zIndex: 'auto'
        });
      },
      onEnterBack: () => {
        // When scrolling back up, slide services back down
        gsap.set(servicesElement, {
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          zIndex: 10
        });

        gsap.to(servicesElement, {
          top: '100vh',
          duration: 0.8,
          ease: "power2.out",
          onComplete: () => {
            gsap.set(servicesElement, {
              position: 'relative',
              top: 'auto',
              zIndex: 'auto'
            });
          }
        });
      }
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === moreCasesSection) {
          trigger.kill();
        }
      });
    };
  }, []);

  return (
    <section
      ref={servicesRef}
      className="bg-black text-white min-h-screen w-full flex items-center justify-center"
      style={{
        fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
      }}
    >
      <div className="container mx-auto px-4 text-center">
        {/* Top label - SERVICES at very left of section */}
        <div style={{
          position: 'absolute',
          top: 120,
          left: 40,
          color: '#fff',
          fontSize: 28,
          fontWeight: 700,
          letterSpacing: 1.5,
          zIndex: 10,
        }}>
          (SERVICES)
        </div>

        {/* Right label - 04 in orange */}
        <div style={{
          position: 'absolute',
          top: 'clamp(420px, 50vh, 580px)',
          right: 'clamp(20px, 3vw, 40px)',
          color: '#e8561c',
          fontSize: 'clamp(21px, 3.36vw, 42px)',
          fontWeight: 700,
          letterSpacing: 1.5,
          zIndex: 10,
        }}>
          (04)
        </div>

        <h2 className="text-3xl font-bold mb-8">Services</h2>
        <div>
          <p className="text-lg">No services available at the moment.</p>
        </div>
      </div>
    </section>
  );
}


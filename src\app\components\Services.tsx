'use client';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export default function Services() {
  const servicesRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const servicesElement = servicesRef.current;
    if (!servicesElement) return;

    // Wait a bit for the DOM to be fully loaded
    const timer = setTimeout(() => {
      // Find the "more cases coming soon" section in Work component
      const moreCasesSection = Array.from(document.querySelectorAll('section')).find(section =>
        section.textContent?.includes('more cases coming soon')
      );

      if (!moreCasesSection) {
        console.warn('More cases section not found');
        return;
      }

      // Create ScrollTrigger for the overlay effect
      ScrollTrigger.create({
        trigger: moreCasesSection,
        start: "center bottom", // Start when "more cases" section center reaches bottom of viewport
        end: "bottom top", // End when "more cases" section bottom reaches top of viewport
        scrub: 1.5, // Smoother scrub
        pin: false,
        onUpdate: (self) => {
          const progress = self.progress;

          if (progress >= 0 && progress <= 1) {
            // Set to fixed position during animation
            gsap.set(servicesElement, {
              position: 'fixed',
              left: 0,
              width: '100vw',
              height: '100vh',
              zIndex: 10,
              overflow: 'hidden'
            });

            // Slide from bottom (100vh) to top (0) based on progress
            // Use easing for smoother movement
            const easedProgress = progress; // Linear for now, can add easing later
            const yPos = 100 - (easedProgress * 100);

            gsap.set(servicesElement, {
              top: `${yPos}vh`
            });
          }
        },
        onLeave: () => {
          // When animation is complete, make it normal positioned
          gsap.set(servicesElement, {
            position: 'relative',
            top: 'auto',
            height: 'auto',
            width: 'auto',
            zIndex: 'auto',
            overflow: 'visible'
          });
        },
        onEnterBack: () => {
          // When scrolling back, start with fixed position
          gsap.set(servicesElement, {
            position: 'fixed',
            left: 0,
            width: '100vw',
            height: '100vh',
            zIndex: 10,
            overflow: 'hidden',
            top: 0
          });
        },
        onLeaveBack: () => {
          // When scrolling back past trigger, reset to normal
          gsap.set(servicesElement, {
            position: 'relative',
            top: 'auto',
            height: 'auto',
            width: 'auto',
            zIndex: 'auto',
            overflow: 'visible'
          });
        }
      });

    }, 100);

    return () => {
      clearTimeout(timer);
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section
      ref={servicesRef}
      className="bg-black text-white min-h-screen w-full flex items-center justify-center"
      style={{
        fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
        overflow: 'hidden', // Prevent scrollbars when fixed
        position: 'relative'
      }}
    >
      <div className="container mx-auto px-4 text-center">
        {/* Top label - SERVICES at very left of section */}
        <div style={{
          position: 'absolute',
          top: 120,
          left: 40,
          color: '#fff',
          fontSize: 28,
          fontWeight: 700,
          letterSpacing: 1.5,
          zIndex: 10,
        }}>
          (SERVICES)
        </div>

        {/* Right label - 04 in orange */}
        <div style={{
          position: 'absolute',
          top: 'clamp(420px, 50vh, 580px)',
          right: 'clamp(20px, 3vw, 40px)',
          color: '#e8561c',
          fontSize: 'clamp(21px, 3.36vw, 42px)',
          fontWeight: 700,
          letterSpacing: 1.5,
          zIndex: 10,
        }}>
          (04)
        </div>

        <h2 className="text-3xl font-bold mb-8">Services</h2>
        <div>
          <p className="text-lg">No services available at the moment.</p>
        </div>
      </div>
    </section>
  );
}

